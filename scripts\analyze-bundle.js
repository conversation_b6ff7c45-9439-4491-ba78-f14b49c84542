#!/usr/bin/env node

import { execSync } from 'child_process';
import { readFileSync, readdirSync, statSync } from 'fs';
import { join } from 'path';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function analyzeDistFolder() {
  const distPath = './dist';
  
  try {
    const files = readdirSync(distPath);
    const jsFiles = files.filter(file => file.endsWith('.js'));
    const cssFiles = files.filter(file => file.endsWith('.css'));
    
    console.log(`${colors.bright}${colors.blue}📦 Bundle Analysis${colors.reset}\n`);
    
    // Analyze JavaScript files
    console.log(`${colors.bright}JavaScript Files:${colors.reset}`);
    let totalJsSize = 0;
    
    jsFiles.forEach(file => {
      const filePath = join(distPath, file);
      const stats = statSync(filePath);
      const size = stats.size;
      totalJsSize += size;
      
      let color = colors.green;
      if (size > 500 * 1024) color = colors.red;
      else if (size > 250 * 1024) color = colors.yellow;
      
      console.log(`  ${color}${file}: ${formatBytes(size)}${colors.reset}`);
    });
    
    console.log(`  ${colors.bright}Total JS: ${formatBytes(totalJsSize)}${colors.reset}\n`);
    
    // Analyze CSS files
    console.log(`${colors.bright}CSS Files:${colors.reset}`);
    let totalCssSize = 0;
    
    cssFiles.forEach(file => {
      const filePath = join(distPath, file);
      const stats = statSync(filePath);
      const size = stats.size;
      totalCssSize += size;
      
      let color = colors.green;
      if (size > 100 * 1024) color = colors.red;
      else if (size > 50 * 1024) color = colors.yellow;
      
      console.log(`  ${color}${file}: ${formatBytes(size)}${colors.reset}`);
    });
    
    console.log(`  ${colors.bright}Total CSS: ${formatBytes(totalCssSize)}${colors.reset}\n`);
    
    // Performance recommendations
    console.log(`${colors.bright}${colors.magenta}📊 Performance Recommendations:${colors.reset}`);
    
    if (totalJsSize > 500 * 1024) {
      console.log(`  ${colors.red}⚠️  Large JS bundle (${formatBytes(totalJsSize)}). Consider:${colors.reset}`);
      console.log(`     - Code splitting with dynamic imports`);
      console.log(`     - Lazy loading non-critical components`);
      console.log(`     - Tree shaking unused dependencies`);
    } else if (totalJsSize > 250 * 1024) {
      console.log(`  ${colors.yellow}⚠️  Medium JS bundle (${formatBytes(totalJsSize)}). Consider optimizing.${colors.reset}`);
    } else {
      console.log(`  ${colors.green}✅ JS bundle size is good (${formatBytes(totalJsSize)})${colors.reset}`);
    }
    
    if (totalCssSize > 100 * 1024) {
      console.log(`  ${colors.red}⚠️  Large CSS bundle (${formatBytes(totalCssSize)}). Consider:${colors.reset}`);
      console.log(`     - Purging unused CSS`);
      console.log(`     - Critical CSS extraction`);
    } else if (totalCssSize > 50 * 1024) {
      console.log(`  ${colors.yellow}⚠️  Medium CSS bundle (${formatBytes(totalCssSize)}). Consider optimizing.${colors.reset}`);
    } else {
      console.log(`  ${colors.green}✅ CSS bundle size is good (${formatBytes(totalCssSize)})${colors.reset}`);
    }
    
    console.log(`\n${colors.bright}Total Bundle Size: ${formatBytes(totalJsSize + totalCssSize)}${colors.reset}`);
    
  } catch (error) {
    console.error(`${colors.red}Error analyzing bundle: ${error.message}${colors.reset}`);
    console.log(`${colors.yellow}Run 'npm run build' first to generate the dist folder.${colors.reset}`);
  }
}

function main() {
  console.log(`${colors.cyan}Building project for analysis...${colors.reset}`);
  
  try {
    execSync('npm run build', { stdio: 'inherit' });
    console.log(`${colors.green}Build completed successfully!${colors.reset}\n`);
    analyzeDistFolder();
  } catch (error) {
    console.error(`${colors.red}Build failed: ${error.message}${colors.reset}`);
    process.exit(1);
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
