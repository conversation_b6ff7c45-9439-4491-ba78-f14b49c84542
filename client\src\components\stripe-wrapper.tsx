import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowLeft, CreditCard, Lock, Loader2 } from 'lucide-react';
import { loadStripe } from '@stripe/stripe-js';
import {
  Elements,
  CardElement,
  useStripe,
  useElements
} from '@stripe/react-stripe-js';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/lib/queryClient';

// Load Stripe only when this component is loaded
const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY);

interface CheckoutFormProps {
  onBack: () => void;
  onSuccess: (amount: string) => void;
}

function CheckoutForm({ onBack, onSuccess }: CheckoutFormProps) {
  const stripe = useStripe();
  const elements = useElements();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [useDiscount, setUseDiscount] = useState(false);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    setLoading(true);

    try {
      // Get the current user session
      const { data: { session } } = await supabase.auth.getSession();

      console.log('Current session:', session ? 'exists' : 'null');
      console.log('User ID:', session?.user?.id);
      console.log('Access token exists:', !!session?.access_token);

      if (!session) {
        console.error('No session found');
        toast({
          title: "Authentication Required",
          description: "Please sign in to continue with payment",
          variant: "destructive",
        });
        setLoading(false);
        return;
      }

      // Create payment intent
      const priceId = useDiscount 
        ? import.meta.env.VITE_STRIPE_PRICE_ID_DISCOUNT 
        : import.meta.env.VITE_STRIPE_PRICE_ID_ORIGINAL;

      console.log('Creating payment intent with:', {
        priceId,
        successUrl: `${window.location.origin}/payment-success`,
        cancelUrl: window.location.href,
      });

      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/stripe-payment`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`,
        },
        body: JSON.stringify({
          priceId,
          successUrl: `${window.location.origin}/payment-success`,
          cancelUrl: window.location.href,
        }),
      });

      console.log('Payment API response status:', response.status);
      const responseData = await response.json();
      console.log('Payment API response data:', responseData);

      const { paymentIntent, error: apiError } = responseData;

      if (apiError) {
        throw new Error(apiError);
      }

      // Confirm payment with Stripe
      const cardElement = elements.getElement(CardElement);
      
      if (!cardElement) {
        throw new Error('Card element not found');
      }

      const { error: stripeError } = await stripe.confirmCardPayment(
        paymentIntent.clientSecret,
        {
          payment_method: {
            card: cardElement,
          }
        }
      );

      if (stripeError) {
        throw new Error(stripeError.message);
      }

      console.log('Payment successful! Showing success modal...');

      toast({
        title: "Payment Successful! 🎉",
        description: "Welcome to ScraperDash Pro! You now have access to all premium features.",
      });

      // Call onSuccess with the payment amount
      const amount = useDiscount ? '4.99' : '9.99';
      onSuccess(amount);
    } catch (error) {
      console.error('Payment error:', error);
      toast({
        title: "Payment Failed",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const cardElementOptions = {
    style: {
      base: {
        fontSize: '16px',
        color: '#ffffff',
        '::placeholder': {
          color: '#9ca3af',
        },
        backgroundColor: 'transparent',
      },
      invalid: {
        color: '#ef4444',
      },
    },
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Discount Toggle */}
      <div className="bg-gradient-to-r from-green-500/10 to-emerald-500/10 border border-green-500/20 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-green-400 font-semibold">Limited Time Offer!</h3>
            <p className="text-gray-300 text-sm">Save 50% on your first month</p>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-green-400">
              ${useDiscount ? '4.99' : '9.99'}
            </div>
            {!useDiscount && (
              <button
                type="button"
                onClick={() => setUseDiscount(true)}
                className="text-green-400 text-sm underline hover:text-green-300"
              >
                Apply 50% discount
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Card Element */}
      <div className="space-y-2">
        <label className="text-sm font-medium text-gray-300 flex items-center">
          <CreditCard className="w-4 h-4 mr-2" />
          Card Information
        </label>
        <div className="p-4 border border-gray-600 rounded-lg bg-gray-800/50">
          <CardElement options={cardElementOptions} />
        </div>
      </div>

      {/* Security Notice */}
      <div className="flex items-center text-sm text-gray-400">
        <Lock className="w-4 h-4 mr-2" />
        Your payment information is secure and encrypted
      </div>

      <div className="flex space-x-3">
        <Button
          type="button"
          variant="outline"
          onClick={onBack}
          className="flex-1 border-gray-600 text-gray-300 hover:bg-gray-800"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back
        </Button>
        <Button
          type="submit"
          disabled={!stripe || loading}
          className="flex-1 bg-gradient-to-r from-cyan-500 to-purple-500 hover:from-cyan-600 hover:to-purple-600"
        >
          {loading ? (
            <>
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              Processing...
            </>
          ) : (
            <>
              Pay ${useDiscount ? '4.99' : '9.99'}
            </>
          )}
        </Button>
      </div>
    </form>
  );
}

interface StripeWrapperProps {
  onBack: () => void;
  onSuccess: (amount: string) => void;
}

export default function StripeWrapper({ onBack, onSuccess }: StripeWrapperProps) {
  return (
    <Elements stripe={stripePromise}>
      <CheckoutForm onBack={onBack} onSuccess={onSuccess} />
    </Elements>
  );
}
