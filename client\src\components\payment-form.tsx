import { useState, useEffect, Suspense, lazy } from 'react';
import { Di<PERSON>, DialogContent, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { ArrowLeft, CreditCard, Lock, Loader2 } from 'lucide-react';
import { motion } from 'framer-motion';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/lib/queryClient';
import PaymentSuccessModal from './payment-success-modal';

// Lazy load Stripe components to reduce initial bundle size
const LazyStripeWrapper = lazy(() => import('./stripe-wrapper'));

interface PaymentFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onBack: () => void;
}

interface CheckoutFormProps {
  onBack: () => void;
  onSuccess: (amount: string) => void;
}

// Loading fallback component
function PaymentFormLoader() {
  return (
    <div className="space-y-6">
      <div className="bg-gradient-to-r from-green-500/10 to-emerald-500/10 border border-green-500/20 rounded-lg p-4">
        <div className="flex items-center justify-center">
          <Loader2 className="w-6 h-6 animate-spin text-green-400" />
          <span className="ml-2 text-green-400">Loading payment form...</span>
        </div>
      </div>
    </div>
  );
}

export default function PaymentForm({ open, onOpenChange, onBack }: PaymentFormProps) {
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [paymentAmount, setPaymentAmount] = useState<string>('');

  const handleSuccess = (amount: string) => {
    setPaymentAmount(amount);
    onOpenChange(false);
    setShowSuccessModal(true);
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-md bg-gray-900 border-gray-700 text-white">
          <DialogHeader>
            <DialogTitle className="text-center">
              <span className="bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent">
                Upgrade to Pro
              </span>
            </DialogTitle>
          </DialogHeader>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <Suspense fallback={<PaymentFormLoader />}>
              <LazyStripeWrapper onBack={onBack} onSuccess={handleSuccess} />
            </Suspense>
          </motion.div>
        </DialogContent>
      </Dialog>

      <PaymentSuccessModal
        open={showSuccessModal}
        onOpenChange={setShowSuccessModal}
        amount={paymentAmount}
      />
    </>
  );
}
